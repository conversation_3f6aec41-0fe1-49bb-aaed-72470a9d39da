import { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>a, <PERSON>let, <PERSON>rip<PERSON>, ScrollRestoration, useLocation } from '@remix-run/react';
import { LinksFunction, json } from '@remix-run/cloudflare';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/cloudflare';
import WalletProvider from '@/wallets/wallet-provider';
import { Provider as Jo<PERSON>Provider } from 'jotai';
import * as gtag from '@/lib/gtags.client';

import styles from '@/index.css?url';
import bourton from '@/assets/fonts/BourtonBaseDrop.woff2?url';
import hudson from '@/assets/fonts/HudsonNYPro-Regular.woff2?url';
import i18nServer from '@/modules/i18n.server';
import { store } from './store';
import { I18nFonts } from './components/i18n/styles';
import '@suiet/wallet-kit/style.css';
import '@/styles/suiet-override.css';

import { withSentry } from '@sentry/remix';
import { Provider as MetricsProvider } from './wallets/metrics-provider';
import { TooltipProvider } from './components/ui/tooltip';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const t = await i18nServer.getFixedT(request);
  return json({ metadata: { description: t('meta.description') } });
};

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: 'Bitlayer' },
    {
      name: 'description',
      content: data?.metadata.description,
    },
  ];
};

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=0" />
        <Meta />
        <Links />
        <I18nFonts />
      </head>
      <body>
        <script async src={`https://www.googletagmanager.com/gtag/js?id=G-THLS255HKW`} />
        {/*<script src="https://cdn.jsdelivr.net/npm/eruda"></script>
        <script>eruda.init();</script>*/}
        <script
          async
          id="gtag-init"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-THLS255HKW', {
                page_path: window.location.pathname,
              });
            `,
          }}
        />
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

function App() {
  const location = useLocation();

  useEffect(() => {
    gtag.pageview(location.pathname, gtag.gaTrackingId);
  }, [location.pathname]);

  return (
    <JotaiProvider store={store}>
      <WalletProvider>
        <MetricsProvider>
          <TooltipProvider>
            <Outlet />
          </TooltipProvider>
        </MetricsProvider>
      </WalletProvider>
    </JotaiProvider>
  );
}

export default withSentry(App);

export { ErrorBoundary } from '@/components/featured/error-boundary';

export const links: LinksFunction = () => [
  { rel: 'stylesheet', href: styles },
  { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
  { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
  { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' },

  // fonts
  { rel: 'prelaod', href: hudson, as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' },
  { rel: 'prelaod', href: bourton, as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' },
  { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
  { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossOrigin: 'anonymous' },
];
