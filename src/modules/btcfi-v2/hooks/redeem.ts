import { useMutation } from '@tanstack/react-query';
import {
  Config,
  estimateFeesPerGas,
  estimateGas,
  waitForTransactionReceipt,
  writeContract,
} from '@wagmi/core';
import { Address, encodeFunctionData } from 'viem';
import { useConfig } from 'wagmi';
import { abi as ybtcAbi } from '../abi/ybtc.b';
import { abi as ybtcStakingAbi } from '../abi/ybtc-staking-vault';
import { handleException } from '@/modules/bridge/transactions/evm.client';
import { Token } from '@/wallets/config/type';
import { BtcfiV2CustodyContractType } from '@/modules/btcfi/types';

type RedeemParams = {
  address: Address;
  amount: bigint;
  token: Token;
  contract: Address;
  contractType: BtcfiV2CustodyContractType;
};

export function useRedeem() {
  const config = useConfig() as Config;

  const { mutate, mutateAsync, ...mutation } = useMutation({
    mutationKey: ['btcfi-v2/redeem'],
    mutationFn: async (params: RedeemParams) => {
      try {
        if (params.contractType === 'yBTC.B') {
          await ybtcRedeem(config, params);
        } else {
          await requestClaim(config, params);
        }
      } catch (err) {
        handleException(err, 'Failed to redeem');
      }
    },
  });

  return {
    redeem: mutate,
    redeemAsync: mutateAsync,
    ...mutation,
  };
}

async function ybtcRedeem(config: Config, params: RedeemParams) {
  const functionName = 'withdraw';
  const args = [params.amount] as const;

  const feesPerGas = await estimateFeesPerGas(config);
  const gas = await estimateGas(config, {
    data: encodeFunctionData({
      abi: ybtcAbi,
      functionName,
      args,
    }),
    to: params.contract,
  });

  const tx = await writeContract(config, {
    abi: ybtcAbi,
    address: params.contract,
    functionName,
    args,
    gas,
    maxFeePerGas: feesPerGas.maxFeePerGas,
    maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
  });
  await waitForTransactionReceipt(config, {
    hash: tx,
    pollingInterval: 5_000,
  });
}

async function requestClaim(config: Config, params: RedeemParams) {
  const functionName = 'requestClaim';
  const args = [params.amount] as const;

  const feesPerGas = await estimateFeesPerGas(config);
  const gas = await estimateGas(config, {
    data: encodeFunctionData({
      abi: ybtcStakingAbi,
      functionName,
      args,
    }),
    to: params.contract,
  });

  const tx = await writeContract(config, {
    abi: ybtcStakingAbi,
    address: params.contract,
    functionName,
    args,
    gas,
    maxFeePerGas: feesPerGas.maxFeePerGas,
    maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
  });

  await waitForTransactionReceipt(config, {
    hash: tx,
    pollingInterval: 5_000,
  });
}
