import { useMutation } from '@tanstack/react-query';
import {
  Config,
  estimateFeesPerGas,
  estimateGas,
  waitForTransactionReceipt,
  writeContract,
} from '@wagmi/core';
import { Address, encodeFunctionData } from 'viem';
import { useConfig } from 'wagmi';
import { abi as ybtcAbi } from '../abi/ybtc.b';
import { abi as ybtcStakingAbi } from '../abi/ybtc-staking-vault';
import { approveErc20, handleException } from '@/modules/bridge/transactions/evm.client';
import { BtcfiV2CustodyContractType } from '@/modules/btcfi/types';
import { ERC20Token, Token } from '@/wallets/config/type';
import { BigNumber } from 'ethers';

type DepositParams = {
  address: Address;
  amount: bigint;
  token: Token;
  contract: Address;
  contractType: BtcfiV2CustodyContractType;
};

export function useDeposit() {
  const config = useConfig() as Config;

  const { mutate, mutateAsync, ...mutation } = useMutation({
    mutationKey: ['btcfi-v2/deposit'],
    mutationFn: async (params: DepositParams) => {
      try {
        if (params.contractType === 'yBTC.B') {
          await ybtcDeposit(config, params);
        } else {
          if (params.token.type === 'erc20') {
            await ybtcStakingDeposit(config, params);
          } else {
            await ybtcStakingDepositNative(config, params);
          }
        }
      } catch (err) {
        handleException(err, 'Failed to deposit');
      }
    },
  });

  return {
    deposit: mutate,
    depositAsync: mutateAsync,
    ...mutation,
  };
}

async function ybtcDeposit(config: Config, params: DepositParams) {
  const functionName = 'deposit';
  const value = params.amount;

  const feesPerGas = await estimateFeesPerGas(config);
  const gas = await estimateGas(config, {
    data: encodeFunctionData({
      abi: ybtcAbi,
      functionName,
    }),
    value,
    to: params.contract,
  });

  const tx = await writeContract(config, {
    abi: ybtcAbi,
    address: params.contract,
    functionName,
    value,
    gas,
    maxFeePerGas: feesPerGas.maxFeePerGas,
    maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
  });

  await waitForTransactionReceipt(config, {
    hash: tx,
    pollingInterval: 5_000,
  });
}

async function ybtcStakingDeposit(config: Config, params: DepositParams) {
  await approveErc20(
    params.contract,
    { config },
    {
      from: params.address,
      amount: BigNumber.from(params.amount),
      token: params.token as ERC20Token,
    },
  );

  const functionName = 'deposit';
  const args = [params.amount] as const;

  const feesPerGas = await estimateFeesPerGas(config);
  const gas = await estimateGas(config, {
    data: encodeFunctionData({
      abi: ybtcStakingAbi,
      functionName,
      args,
    }),
    to: params.contract,
  });

  const tx = await writeContract(config, {
    abi: ybtcStakingAbi,
    address: params.contract,
    functionName,
    args,
    gas,
    maxFeePerGas: feesPerGas.maxFeePerGas,
    maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
  });

  await waitForTransactionReceipt(config, {
    hash: tx,
    pollingInterval: 5_000,
  });
}

async function ybtcStakingDepositNative(config: Config, params: DepositParams) {
  const functionName = 'depositNative';

  const feesPerGas = await estimateFeesPerGas(config);
  const gas = await estimateGas(config, {
    data: encodeFunctionData({
      abi: ybtcStakingAbi,
      functionName,
    }),
    to: params.contract,
    value: params.amount,
  });

  const tx = await writeContract(config, {
    abi: ybtcStakingAbi,
    address: params.contract,
    functionName,
    value: params.amount,
    gas,
    maxFeePerGas: feesPerGas.maxFeePerGas,
    maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
  });

  await waitForTransactionReceipt(config, {
    hash: tx,
    pollingInterval: 5_000,
  });
}

type MaxDepositAmountParams = DepositParams & {
  balance: bigint;
};

export function useMaxDepositAmount() {
  const config = useConfig() as Config;

  const { mutate, mutateAsync, ...mutation } = useMutation({
    mutationFn: async (params: MaxDepositAmountParams) => {
      try {
        if (params.contractType === 'yBTC.B') {
          const functionName = 'deposit';
          const value = params.amount;

          const feesPerGas = await estimateFeesPerGas(config);
          const gas = await estimateGas(config, {
            data: encodeFunctionData({
              abi: ybtcAbi,
              functionName,
            }),
            value,
            to: params.contract,
            maxFeePerGas: feesPerGas.maxFeePerGas,
            maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
          });

          return params.balance - gas * feesPerGas.maxFeePerGas;
        } else {
          return params.balance;
        }
      } catch (err) {
        console.warn('Failed to get max deposit amount', err);
        return params.balance;
      }
    },
  });

  return {
    queryMax: mutate,
    queryMaxAsync: mutateAsync,
    ...mutation,
  };
}
