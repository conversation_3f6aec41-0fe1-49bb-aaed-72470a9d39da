import SolutionSection from './_index/section.solution';
import TitleSection from './_index/section.title';
import Spotlight from './_index/section.spotlight.tsx';
import BitvmSection from './_index/section.bitvm.tsx';

import type { LoaderFunctionArgs } from '@remix-run/cloudflare';
import { json, useLoaderData } from '@remix-run/react';
import { TotalResponse } from '@/lib/api/apps-center.client';
import { TVlDataContent } from './_index/section.live-data';
import OurInvestorsSection from './_index/section.investors.tsx';
import EcosystemSection from './_index/section.ecosystem.tsx';
import { Gallary } from '@/components/ui/gallary';
import { PageLoader } from '@/components/featured/page-loader';
import { cn } from '@/lib/utils.ts';
import { useState } from 'react';
import { BitVMBridgeSection } from './_index/section-bitvm-bridge.tsx';
import { BitlayerNetworkSection } from './_index/section-bitlayer-network.tsx';
import { IDOSection } from './_index/section.ido.tsx';

export const loader = async ({ context }: LoaderFunctionArgs) => {
  const baseURL = context.cloudflare.env.API_RANKING_LIST_URL;
  const now = new Date();
  try {
    const res = await fetch(`${baseURL}/activity/ready-player-one/v3/total`);
    const { data }: TotalResponse = await res.json();
    return json({
      tvl: data.total_tvl,
      transitions: data.total_tx.toString() || '0',
      appsDeployed: data.total_likes.toString() || '0',
      now,
    });
  } catch (e) {
    const res = {
      tvl: '0',
      transitions: '0',
      appsDeployed: '0',
      now,
    };

    return json(res);
  }
};

export default function HomePage() {
  const pageData = useLoaderData<typeof loader>();
  const [visible, setVisible] = useState(false);

  const handleLoad = () => {
    setVisible(true);
  };

  return (
    <>
      <PageLoader onLoad={handleLoad} />
      <div
        className={cn('bl-w-full bl-duration-200 bl-opacity-0', {
          'bl-opacity-100': visible,
        })}
      >
        <TitleSection />
        <IDOSection />
        {/* <div style={{ cursor: 'url(/images/home/<USER>', }}> */}
        <Gallary className="after:!bl-w-full after:bl-left-0 bl-border-l-0 bl-border-r-0">
          <TVlDataContent tvl={pageData.tvl} />
          <Spotlight />
        </Gallary>
        {/* </div> */}
        <SolutionSection />
        <Gallary className="after:!bl-w-full after:bl-left-0 after:bl-top-0 bl-border-l-0 bl-border-r-0">
          <BitvmSection />
        </Gallary>
        <BitVMBridgeSection />
        <div className="bl-h-1 bl-overflow-hidden">
          <Gallary className="after:!bl-w-full after:bl-left-0 after:bl-bottom-0 bl-border-l-0 bl-border-r-0">
            <div className="bl-h-screen"></div>
          </Gallary>
        </div>

        <BitlayerNetworkSection />
        <Gallary className="after:!bl-w-full after:bl-left-0 after:bl-bottom-0 bl-border-l-0 bl-border-r-0">
          <EcosystemSection />
          <OurInvestorsSection />
        </Gallary>
      </div>
    </>
  );
}
